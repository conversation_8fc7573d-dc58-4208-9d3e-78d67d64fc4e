<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>80.lv 分页脚本测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .problem {
            background-color: #ffebee;
            border-color: #f44336;
        }
        .solution {
            background-color: #e8f5e8;
            border-color: #4caf50;
        }
        .code {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            overflow-x: auto;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <h1>80.lv 分页功能问题分析与解决方案</h1>
    
    <div class="section problem">
        <h2>🔍 问题分析</h2>
        <p>通过实际测试80.lv网站，发现了以下关键问题：</p>
        
        <h3>1. URL结构问题</h3>
        <ul>
            <li>第1页：<code>https://80.lv/articles</code> 或 <code>https://80.lv/articles/</code></li>
            <li>第2页及以后：<span class="highlight">所有分页链接都指向 <code>/articles/</code>，没有页码参数</span></li>
        </ul>
        
        <h3>2. 分页机制</h3>
        <p>从HTML结构可以看到：</p>
        <div class="code">
&lt;ul class="_2pSuN _2TtkW _3enxs"&gt;
  &lt;li class="E2y4M _25VMy"&gt;&lt;!-- 禁用的上一页按钮 --&gt;&lt;/li&gt;
  &lt;li class="E2y4M _3NLRK"&gt;&lt;a href="/articles/"&gt;1&lt;/a&gt;&lt;/li&gt;  &lt;!-- 当前页 --&gt;
  &lt;li class="E2y4M"&gt;&lt;a href="/articles/"&gt;2&lt;/a&gt;&lt;/li&gt;
  &lt;li class="E2y4M"&gt;&lt;a href="/articles/"&gt;3&lt;/a&gt;&lt;/li&gt;
  &lt;li class="E2y4M"&gt;&lt;a href="/articles/"&gt;4&lt;/a&gt;&lt;/li&gt;
  &lt;li class="E2y4M"&gt;&lt;a href="/articles/"&gt;5&lt;/a&gt;&lt;/li&gt;
&lt;/ul&gt;
        </div>
        <p><strong>所有页面链接都指向同一个URL</strong>，这意味着网站使用JavaScript动态加载内容。</p>
        
        <h3>3. 网站使用JavaScript分页</h3>
        <p>80.lv网站很可能使用了：</p>
        <ul>
            <li>AJAX/Fetch API 动态加载内容</li>
            <li>客户端路由（可能是SPA单页应用）</li>
            <li>依赖JavaScript事件处理分页</li>
        </ul>
    </div>
    
    <div class="section solution">
        <h2>✅ 解决方案</h2>
        <p>已更新用户脚本 v2.9 来适应JavaScript驱动的分页机制：</p>
        
        <h3>主要改进：</h3>
        <ol>
            <li><strong>改进页面检测</strong>：优先通过DOM结构中的 <code>_3NLRK</code> 类来识别当前页</li>
            <li><strong>模拟真实点击</strong>：使用 <code>MouseEvent</code> 来模拟用户点击，而不是简单的 <code>click()</code></li>
            <li><strong>异步等待机制</strong>：添加页面更新等待逻辑，适配JavaScript异步加载</li>
            <li><strong>改进错误处理</strong>：更好的错误提示和回退机制</li>
        </ol>
        
        <h3>关键代码改进：</h3>
        <div class="code">
// 模拟真实用户点击
const clickEvent = new MouseEvent('click', {
    bubbles: true,
    cancelable: true,
    view: window,
    detail: 1
});
directTargetLink.dispatchEvent(clickEvent);

// 等待页面更新
setTimeout(() => {
    const newPage = getCurrentPage();
    if (newPage === targetPage) {
        logDebug(`直接跳转成功到页面 ${targetPage}`);
        flashInputElement(pageInput, 'lightgreen', 1000);
    }
}, 500);
        </div>
    </div>
    
    <div class="section">
        <h2>🚀 使用说明</h2>
        <ol>
            <li>安装更新后的用户脚本 v2.9</li>
            <li>访问 <a href="https://80.lv/articles" target="_blank">https://80.lv/articles</a></li>
            <li>脚本会在页面右下角显示导航控件</li>
            <li>使用方法：
                <ul>
                    <li><strong>前进/后退</strong>：点击 ◀ / ▶ 按钮</li>
                    <li><strong>跳转</strong>：在输入框输入页码，按回车或点击"跳转"</li>
                    <li><strong>键盘快捷键</strong>：
                        <ul>
                            <li>Alt + G：聚焦输入框</li>
                            <li>左箭头：上一页</li>
                            <li>右箭头：下一页</li>
                        </ul>
                    </li>
                </ul>
            </li>
        </ol>
    </div>
    
    <div class="section">
        <h2>🔧 技术细节</h2>
        <p>脚本现在能够：</p>
        <ul>
            <li>正确识别80.lv的JavaScript分页机制</li>
            <li>通过DOM结构而非URL来确定当前页码</li>
            <li>模拟真实用户交互来触发页面切换</li>
            <li>处理异步页面更新</li>
            <li>提供视觉反馈和错误处理</li>
        </ul>
    </div>
    
    <script>
        // 简单的测试脚本
        console.log('80.lv 分页脚本测试页面已加载');
        
        // 模拟检查脚本是否正常工作
        function checkScript() {
            const hasUserScript = window.location.href.includes('80.lv');
            if (hasUserScript) {
                console.log('✅ 在80.lv网站上，用户脚本应该已激活');
            } else {
                console.log('ℹ️ 这是测试页面，用户脚本仅在80.lv网站上工作');
            }
        }
        
        setTimeout(checkScript, 1000);
    </script>
</body>
</html>
