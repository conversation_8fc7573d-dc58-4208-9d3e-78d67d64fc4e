{"name": "80lv-navigation-enhancement", "version": "2.8.0", "description": "Enhanced navigation for 80.lv articles page with Playwright testing", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "install-browsers": "playwright install"}, "keywords": ["userscript", "navigation", "80.lv", "playwright", "testing"], "author": "AI Assistant", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0"}, "engines": {"node": ">=16.0.0"}}