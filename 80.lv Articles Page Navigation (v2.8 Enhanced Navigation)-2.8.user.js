// ==UserScript==
// @name         80.lv Articles Page Navigation (v2.9 JavaScript分页修复版)
// @namespace    http://tampermonkey.net/
// @version      2.9
// @description  修复80.lv/articles页面JavaScript分页导航：前进/后退按钮 + 跳转输入框，适配网站的动态分页机制
// <AUTHOR> (Revised by AI)
// @match        https://80.lv/articles*
// @exclude      https://80.lv/articles/*/amp* // 排除 AMP 页面
// @icon         https://www.google.com/s2/favicons?sz=64&domain=80.lv
// @grant        none
// @license      MIT
// ==/UserScript==

(function() {
    'use strict';

    // --- 配置 ---
    const CONFIG = {
        // 基于实际测试更新的选择器
        selectors: {
            paginationContainer: 'ul._2pSuN', // 分页容器
            pageLink: 'ul._2pSuN li.E2y4M a._2ZymR', // 页面数字链接
            activePageLink: 'li.E2y4M._3NLRK a._2ZymR', // 当前激活页面链接

            // --- 网站原生前进/后退按钮选择器 ---
            sitePrevButton: 'ul._2pSuN > li:first-child > *._2ZymR', // 前一页按钮
            siteNextButton: 'ul._2pSuN > li:last-child > *._2ZymR', // 下一页按钮

            // 禁用状态检测
            disabledButton: 'button[disabled], .disabled',
            disabledListItem: 'li._25VMy, li.disabled'
        },
        uiIds: {
            container: 'tm-page-nav-container-80lv-v28',
            input: 'tm-page-input-80lv-v28',
            info: 'tm-page-info-80lv-v28',
            prevButton: 'tm-page-prev-button-80lv-v28',
            nextButton: 'tm-page-next-button-80lv-v28',
            goButton: 'tm-page-go-button-80lv-v28',
            closeButton: 'tm-page-close-button-80lv-v28',
            styleTag: 'tm-page-nav-styles-80lv-v28'
        },
        behavior: {
            focusShortcutKey: 'g', // Alt + 键
            prevPageShortcutKey: 'ArrowLeft', // 左箭头键
            nextPageShortcutKey: 'ArrowRight', // 右箭头键
            observerDebounceMs: 300, // 减少延迟提高响应性
            maxRetries: 3, // 最大重试次数
            retryDelay: 1000 // 重试延迟（毫秒）
        },
        sessionStorageKeys: {
            targetPage: 'tm_80lv_targetPage_v28',
            isActive: 'tm_80lv_navigatingActive_v28',
            retryCount: 'tm_80lv_retryCount_v28'
        }
    };

    // --- 状态管理 ---
    let state = {
        uiInitialized: false,
        observer: null,
        observerTimeout: null,
        cachedCurrentPage: 1,
        cachedMaxVisiblePage: 1,
        lastNavigationMethod: null,
        isNavigating: false,
        retryCount: 0
    };

    // --- 日志记录 ---
    const DEBUG_MODE = true;
    function logDebug(...args) {
        if (DEBUG_MODE) {
            console.log('[80lv 导航脚本 v2.9]', ...args);
        }
    }
    function logError(...args) {
        console.error('[80lv 导航脚本 v2.9]', ...args);
    }
    function logWarn(...args) {
        console.warn('[80lv 导航脚本 v2.9]', ...args);
    }

    // --- 核心逻辑 ---
    function getCurrentPage() {
        try {
            // 1. 优先从激活的链接获取当前页面（80.lv使用_3NLRK类标识当前页）
            const activeLink = document.querySelector(CONFIG.selectors.activePageLink);
            if (activeLink?.textContent) {
                const pageNum = parseInt(activeLink.textContent.trim(), 10);
                if (!isNaN(pageNum) && pageNum > 0) {
                    logDebug('通过激活链接找到当前页面:', pageNum);
                    state.cachedCurrentPage = pageNum;
                    return pageNum;
                }
            }

            // 2. 80.lv的JavaScript分页不改变URL，所以检查DOM结构
            // 查找所有分页链接，找到没有链接属性或特殊样式的当前页
            const paginationContainer = document.querySelector(CONFIG.selectors.paginationContainer);
            if (paginationContainer) {
                const pageItems = paginationContainer.querySelectorAll('li.E2y4M');
                for (const item of pageItems) {
                    // 检查是否有_3NLRK类（当前页标识）
                    if (item.classList.contains('_3NLRK')) {
                        const link = item.querySelector('a');
                        if (link?.textContent) {
                            const pageNum = parseInt(link.textContent.trim(), 10);
                            if (!isNaN(pageNum) && pageNum > 0) {
                                logDebug('通过DOM结构找到当前页面:', pageNum);
                                state.cachedCurrentPage = pageNum;
                                return pageNum;
                            }
                        }
                    }
                }
            }

            // 3. 如果在基础articles页面且没有找到分页，默认为第1页
            if (window.location.pathname === '/articles' || window.location.pathname === '/articles/') {
                logDebug('在基础articles页面，默认为第1页');
                state.cachedCurrentPage = 1;
                return 1;
            }

            // 4. 回退到缓存值
            logDebug('无法确定当前页面，使用缓存值:', state.cachedCurrentPage);
            return state.cachedCurrentPage || 1;

        } catch (e) {
            logError("获取当前页面时出错:", e);
            return state.cachedCurrentPage || 1;
        }
    }

    function getMaxVisiblePageNumber() {
        let maxPage = 1;
        try {
            const container = document.querySelector(CONFIG.selectors.paginationContainer);
            if (!container) {
                logDebug('未找到分页容器');
                return state.cachedMaxVisiblePage || state.cachedCurrentPage || 1;
            }

            const paginationLinks = container.querySelectorAll(CONFIG.selectors.pageLink);
            if (paginationLinks.length === 0) {
                maxPage = Math.max(1, state.cachedCurrentPage);
                logDebug('未找到页面链接，基于当前页面返回最大页面:', maxPage);
                state.cachedMaxVisiblePage = maxPage;
                return maxPage;
            }

            // 遍历所有分页链接，找到最大页码
            paginationLinks.forEach(link => {
                if (link?.textContent) {
                    const pageNumText = link.textContent.trim();
                    if (/^\d+$/.test(pageNumText)) {
                        const pageNum = parseInt(pageNumText, 10);
                        if (!isNaN(pageNum) && pageNum > maxPage) {
                            maxPage = pageNum;
                        }
                    }
                }
            });

            logDebug('计算得到的最大可见页面:', maxPage);
            state.cachedMaxVisiblePage = maxPage;
            return maxPage;
        } catch (e) {
            logError("获取最大可见页面数时出错:", e);
            maxPage = Math.max(1, state.cachedCurrentPage);
            state.cachedMaxVisiblePage = maxPage;
            return maxPage;
        }
    }

    function findPageLinkElement(pageNumber) {
        logDebug(`尝试查找页面 ${pageNumber} 的链接元素`);
        try {
            const container = document.querySelector(CONFIG.selectors.paginationContainer);
            if (!container) {
                logDebug("未找到分页容器");
                return null;
            }

            const pageLinks = container.querySelectorAll(CONFIG.selectors.pageLink);
            for (const link of pageLinks) {
                const linkText = link?.textContent?.trim();
                if (linkText && linkText === String(pageNumber)) {
                    logDebug(`找到页面 ${pageNumber} 的匹配链接:`, link);
                    return link;
                }
            }
            logDebug(`未找到页面 ${pageNumber} 的可见链接`);
        } catch (e) {
            logError(`查找页面 ${pageNumber} 链接时出错:`, e);
        }
        return null;
    }

    // --- 改进的按钮状态检测 ---
    function isButtonDisabled(button) {
        if (!button) return true;

        // 检查按钮本身的禁用状态
        if (button.disabled || button.hasAttribute('disabled')) {
            return true;
        }

        // 检查父级 li 元素的禁用类
        const parentLi = button.closest('li');
        if (parentLi) {
            const disabledClasses = ['_25VMy', 'disabled', '_3NLRK'];
            if (disabledClasses.some(cls => parentLi.classList.contains(cls))) {
                return true;
            }
        }

        return false;
    }

    // --- 顺序导航逻辑 ---
    function clearSequentialNavState() {
        logDebug("清除顺序导航状态");
        try {
            sessionStorage.removeItem(CONFIG.sessionStorageKeys.targetPage);
            sessionStorage.removeItem(CONFIG.sessionStorageKeys.isActive);
            sessionStorage.removeItem(CONFIG.sessionStorageKeys.retryCount);
            state.isNavigating = false;
            state.retryCount = 0;
        } catch (e) {
            logError("清除sessionStorage时出错:", e);
        }
    }

    function startSequentialNavigation() {
        const pageInput = document.getElementById(CONFIG.uiIds.input);
        if (!pageInput) {
            logError("顺序导航启动时缺少输入字段");
            return;
        }

        // 防止重复导航
        if (state.isNavigating) {
            logWarn("导航正在进行中，忽略新的导航请求");
            return;
        }

        let targetPage;
        try {
            targetPage = parseInt(pageInput.value, 10);
            logDebug(`请求顺序导航. 输入值: ${pageInput.value}, 解析目标: ${targetPage}`);
        } catch (e) {
            logError("解析页面输入时出错:", e);
            flashInputElement(pageInput, 'red');
            clearSequentialNavState();
            return;
        }

        if (isNaN(targetPage) || targetPage < 1) {
            logError(`无效的目标页面: ${targetPage}`);
            flashInputElement(pageInput, 'red');
            clearSequentialNavState();
            return;
        }

        const currentPage = getCurrentPage();
        if (currentPage === targetPage) {
            logDebug(`页面 ${targetPage} 已经是当前页面，无需导航`);
            flashInputElement(pageInput, 'lightgreen');
            clearSequentialNavState();
            return;
        }

        try {
            logDebug(`存储目标页面=${targetPage} 并激活顺序导航`);
            sessionStorage.setItem(CONFIG.sessionStorageKeys.targetPage, targetPage.toString());
            sessionStorage.setItem(CONFIG.sessionStorageKeys.isActive, 'true');
            sessionStorage.setItem(CONFIG.sessionStorageKeys.retryCount, '0');
            state.lastNavigationMethod = 'sequential';
            state.isNavigating = true;
            flashInputElement(pageInput, 'orange');

            continueSequentialNavigation();

        } catch (e) {
            logError("设置sessionStorage时出错:", e);
            alert("无法保存导航状态，请查看控制台");
            clearSequentialNavState();
            flashInputElement(pageInput, 'red');
        }
    }

    /**
     * 检查顺序导航是否激活并执行下一步
     * 针对80.lv的JavaScript分页进行优化：直接模拟点击分页链接
     */
    function continueSequentialNavigation() {
        let targetPageStr, isActiveStr;
        try {
            targetPageStr = sessionStorage.getItem(CONFIG.sessionStorageKeys.targetPage);
            isActiveStr = sessionStorage.getItem(CONFIG.sessionStorageKeys.isActive);
        } catch (e) {
            logError("SessionStorage读取错误:", e);
            return;
        }

        // 如果不活跃或状态缺失/无效则退出
        if (isActiveStr !== 'true' || !targetPageStr) {
            return;
        }

        const targetPage = parseInt(targetPageStr, 10);
        if (isNaN(targetPage)) {
            logError(`sessionStorage中的目标页面无效: ${targetPageStr}. 中止.`);
            clearSequentialNavState();
            return;
        }

        logDebug(`继续顺序导航. 目标: ${targetPage}`);
        const pageInput = document.getElementById(CONFIG.uiIds.input);
        const currentPage = getCurrentPage();

        // === 1. 完成检查 ===
        if (currentPage === targetPage) {
            logDebug(`顺序导航完成. 到达目标页面 ${targetPage}.`);
            clearSequentialNavState();
            flashInputElement(pageInput, 'lightgreen', 1500);
            if (pageInput && document.activeElement !== pageInput) {
                pageInput.value = currentPage;
            }
            return;
        }

        // === 2. 直接跳转：检查目标页面链接是否可见 ===
        const directTargetLink = findPageLinkElement(targetPage);
        if (directTargetLink) {
            logDebug(`目标页面 ${targetPage} 链接可见. 直接点击.`);
            flashInputElement(pageInput, 'cyan', 1000);
            try {
                clearSequentialNavState();
                state.lastNavigationMethod = 'direct_click';

                // 对于80.lv，我们需要模拟真实的用户点击
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    detail: 1
                });
                directTargetLink.dispatchEvent(clickEvent);

                // 等待页面更新
                setTimeout(() => {
                    const newPage = getCurrentPage();
                    if (newPage === targetPage) {
                        logDebug(`直接跳转成功到页面 ${targetPage}`);
                        flashInputElement(pageInput, 'lightgreen', 1000);
                    } else {
                        logWarn(`直接跳转后页面不匹配，当前: ${newPage}, 目标: ${targetPage}`);
                    }
                }, 500);

            } catch (e) {
                logError(`点击页面 ${targetPage} 直接链接时出错:`, e);
                alert(`点击页面 ${targetPage} 的直接链接时出错. 中止.`);
                clearSequentialNavState();
                flashInputElement(pageInput, 'red');
            }
            return;
        }

        // === 3. 顺序导航：目标不可见，使用前进/后退按钮 ===
        logDebug(`目标页面 ${targetPage} 不可见. 使用网站前进/后退按钮进行顺序导航.`);
        let buttonToClick = null;
        let buttonSelector = '';
        let action = '';

        if (targetPage > currentPage) {
            action = '下一页';
            buttonSelector = CONFIG.selectors.siteNextButton;
        } else {
            action = '上一页';
            buttonSelector = CONFIG.selectors.sitePrevButton;
        }

        try {
            buttonToClick = document.querySelector(buttonSelector);

            if (buttonToClick && !isButtonDisabled(buttonToClick)) {
                logDebug(`找到网站"${action}"按钮. 模拟点击进行顺序导航...`);
                flashInputElement(pageInput, 'lightblue');
                state.lastNavigationMethod = 'sequential';

                // 模拟真实用户点击
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    detail: 1
                });
                buttonToClick.dispatchEvent(clickEvent);

                // 等待页面更新后继续导航
                setTimeout(() => {
                    continueSequentialNavigation();
                }, 800);

            } else {
                if (!buttonToClick) {
                    logWarn(`使用选择器未找到网站"${action}"按钮: "${buttonSelector}". 中止顺序导航.`);
                } else {
                    logWarn(`找到网站"${action}"按钮但已禁用. 中止顺序导航.`);
                }
                alert(`无法找到或点击网站的${action}按钮 (可能已到达页面边界). 中止.`);
                clearSequentialNavState();
                flashInputElement(pageInput, 'red');
                if (pageInput && document.activeElement !== pageInput) {
                    pageInput.value = currentPage;
                }
            }
        } catch (e) {
            logError(`查找/点击网站"${action}"按钮时出错 (选择器: ${buttonSelector}):`, e);
            alert(`${action}点击期间出错. 中止导航. 检查控制台.`);
            clearSequentialNavState();
            flashInputElement(pageInput, 'red');
        }
    }

    // --- 工具函数 ---
    function flashInputElement(element, color, duration = 750) {
        if (!element) return;
        try {
            const originalOutline = element.style.outline || 'none';
            const originalTransition = element.style.transition || '';
            element.style.transition = 'outline 0.1s ease-in-out';
            element.style.outline = `3px solid ${color}`;
            setTimeout(() => {
                if (element) {
                    element.style.outline = originalOutline;
                    element.style.transition = originalTransition;
                }
            }, duration);
        } catch (e) {
            logError("闪烁输入元素时出错:", e);
        }
    }

    // 等待页面内容更新的工具函数
    function waitForPageUpdate(expectedPage, maxWaitTime = 3000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const checkInterval = 200;

            const checkPage = () => {
                const currentPage = getCurrentPage();
                if (currentPage === expectedPage) {
                    logDebug(`页面更新完成，当前页: ${currentPage}`);
                    resolve(currentPage);
                    return;
                }

                if (Date.now() - startTime > maxWaitTime) {
                    logWarn(`等待页面更新超时，期望: ${expectedPage}, 当前: ${currentPage}`);
                    reject(new Error(`页面更新超时`));
                    return;
                }

                setTimeout(checkPage, checkInterval);
            };

            checkPage();
        });
    }

    // --- UI创建和管理 ---
    function injectStyles() {
        if (document.getElementById(CONFIG.uiIds.styleTag)) return;
        logDebug("注入CSS样式...");
        const css = `
            #${CONFIG.uiIds.container} {
                position: fixed;
                bottom: 25px;
                right: 25px;
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.98));
                border: 1px solid rgba(203, 213, 225, 0.8);
                border-radius: 12px;
                padding: 12px 16px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
                z-index: 10001;
                display: flex;
                align-items: center;
                gap: 10px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                font-size: 14px;
                line-height: 1.4;
                color: #334155;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                opacity: 1;
                transform: translateY(0);
                backdrop-filter: blur(8px);
            }
            #${CONFIG.uiIds.container}.hidden {
                opacity: 0;
                transform: translateY(10px);
                pointer-events: none;
            }
            #${CONFIG.uiIds.container} label {
                white-space: nowrap;
                margin-right: 6px;
                font-weight: 500;
                color: #475569;
            }
            #${CONFIG.uiIds.input} {
                width: 60px;
                padding: 8px 10px;
                border: 1px solid #cbd5e1;
                border-radius: 8px;
                font-size: 14px;
                text-align: center;
                transition: all 0.2s ease;
                background: white;
            }
            #${CONFIG.uiIds.input}:focus {
                border-color: #3b82f6;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                outline: none;
            }
            #${CONFIG.uiIds.info} {
                font-size: 13px;
                color: #64748b;
                white-space: nowrap;
                user-select: none;
                margin-left: 4px;
                margin-right: 8px;
            }
            #${CONFIG.uiIds.prevButton}, #${CONFIG.uiIds.nextButton}, #${CONFIG.uiIds.goButton} {
                padding: 8px 12px;
                background: linear-gradient(135deg, #3b82f6, #2563eb);
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.2s ease;
                white-space: nowrap;
                box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
            }
            #${CONFIG.uiIds.prevButton}:hover, #${CONFIG.uiIds.nextButton}:hover, #${CONFIG.uiIds.goButton}:hover {
                background: linear-gradient(135deg, #2563eb, #1d4ed8);
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
            }
            #${CONFIG.uiIds.prevButton}:disabled, #${CONFIG.uiIds.nextButton}:disabled {
                background: #94a3b8;
                cursor: not-allowed;
                opacity: 0.7;
                transform: none;
                box-shadow: none;
            }
            #${CONFIG.uiIds.closeButton} {
                background: none;
                border: none;
                color: #64748b;
                font-size: 18px;
                line-height: 1;
                cursor: pointer;
                padding: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 28px;
                height: 28px;
                border-radius: 6px;
                transition: all 0.2s ease;
                margin-left: 8px;
            }
            #${CONFIG.uiIds.closeButton}:hover {
                background-color: rgba(248, 113, 113, 0.1);
                color: #ef4444;
            }
        `;
        try {
            const styleTag = document.createElement('style');
            styleTag.id = CONFIG.uiIds.styleTag;
            styleTag.textContent = css;
            document.head.appendChild(styleTag);
        } catch (e) {
            logError("注入样式时出错:", e);
        }
    }

    function createOrUpdateUI() {
        logDebug("尝试创建或更新UI...");
        try {
            const currentPage = getCurrentPage();
            const maxVisiblePage = getMaxVisiblePageNumber();

            logDebug(`UI更新状态: currentPage=${currentPage}, maxVisiblePage=${maxVisiblePage}`);

            let container = document.getElementById(CONFIG.uiIds.container);
            let isNewUI = false;

            if (!container) {
                logDebug("未找到UI容器，创建新的UI元素。");
                isNewUI = true;
                container = document.createElement('div');
                container.id = CONFIG.uiIds.container;

                const label = document.createElement('label');
                label.htmlFor = CONFIG.uiIds.input;
                label.textContent = '页面:';
                container.appendChild(label);

                const input = document.createElement('input');
                input.id = CONFIG.uiIds.input;
                input.type = 'number';
                input.min = '1';
                input.placeholder = "跳转";
                input.title = '输入页码并按回车或点击跳转（使用优化的顺序导航）';
                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        clearSequentialNavState();
                        startSequentialNavigation();
                        e.preventDefault();
                    } else {
                        // 任何其他输入都会取消来自上一页加载的待处理导航
                        if (sessionStorage.getItem(CONFIG.sessionStorageKeys.isActive) === 'true') {
                            logDebug("用户在输入中输入，取消待处理的顺序导航。");
                            clearSequentialNavState();
                        }
                    }
                });
                // 防止非数字字符
                input.addEventListener('input', function() {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
                container.appendChild(input);

                const info = document.createElement('span');
                info.id = CONFIG.uiIds.info;
                info.title = '当前在网站分页中可见的最高页码';
                container.appendChild(info);

                // --- 脚本的前进/后退按钮（现在使用网站按钮） ---
                const prevBtn = document.createElement('button');
                prevBtn.id = CONFIG.uiIds.prevButton;
                prevBtn.textContent = '◀';
                prevBtn.title = `转到上一页（点击网站按钮）（← 箭头键）`;
                prevBtn.addEventListener('click', () => {
                    logDebug("脚本的上一页按钮被点击。");
                    clearSequentialNavState();
                    const sitePrevButton = document.querySelector(CONFIG.selectors.sitePrevButton);

                    if (sitePrevButton && !isButtonDisabled(sitePrevButton)) {
                        logDebug("找到活跃的网站上一页按钮。点击它。");
                        flashInputElement(prevBtn, 'lightblue');
                        try {
                            sitePrevButton.click();
                        } catch (e) {
                            logError("点击网站上一页按钮时出错:", e);
                            flashInputElement(prevBtn, 'red');
                        }
                    } else {
                        logWarn("未找到网站上一页按钮或已禁用。");
                        flashInputElement(prevBtn, 'orange');
                        prevBtn.disabled = true;
                    }
                });
                container.appendChild(prevBtn);

                const nextBtn = document.createElement('button');
                nextBtn.id = CONFIG.uiIds.nextButton;
                nextBtn.textContent = '▶';
                nextBtn.title = `转到下一页（点击网站按钮）（→ 箭头键）`;
                nextBtn.addEventListener('click', () => {
                    logDebug("脚本的下一页按钮被点击。");
                    clearSequentialNavState();
                    const siteNextButton = document.querySelector(CONFIG.selectors.siteNextButton);

                    if (siteNextButton && !isButtonDisabled(siteNextButton)) {
                        logDebug("找到活跃的网站下一页按钮。点击它。");
                        flashInputElement(nextBtn, 'lightblue');
                        try {
                            siteNextButton.click();
                        } catch (e) {
                            logError("点击网站下一页按钮时出错:", e);
                            flashInputElement(nextBtn, 'red');
                        }
                    } else {
                        logWarn("未找到网站下一页按钮或已禁用。");
                        flashInputElement(nextBtn, 'orange');
                        nextBtn.disabled = true;
                    }
                });
                container.appendChild(nextBtn);

                // --- 跳转按钮（启动顺序导航） ---
                const goBtn = document.createElement('button');
                goBtn.id = CONFIG.uiIds.goButton;
                goBtn.textContent = '跳转';
                goBtn.title = '顺序导航（优化）到指定页面';
                goBtn.addEventListener('click', () => {
                    clearSequentialNavState();
                    startSequentialNavigation();
                });
                container.appendChild(goBtn);

                const closeBtn = document.createElement('button');
                closeBtn.id = CONFIG.uiIds.closeButton;
                closeBtn.innerHTML = '×';
                closeBtn.title = `隐藏导航控件（Alt+${CONFIG.behavior.focusShortcutKey} 聚焦/显示）`;
                closeBtn.addEventListener('click', () => {
                    if (container) container.classList.add('hidden');
                    clearSequentialNavState();
                });
                container.appendChild(closeBtn);

                container.title = "使用 ◀ / ▶ 进行直接页面更改，或输入页码 + 跳转/回车进行优化的顺序导航。";
                document.body.appendChild(container);
                logDebug("新的UI元素已添加到body。");

            } else {
                logDebug("找到UI容器，更新现有元素。");
            }

            // --- 更新UI状态（对新的和现有的UI都运行） ---
            const inputEl = document.getElementById(CONFIG.uiIds.input);
            const infoEl = document.getElementById(CONFIG.uiIds.info);
            const prevBtnEl = document.getElementById(CONFIG.uiIds.prevButton);

            if (inputEl) {
                const isNavigating = sessionStorage.getItem(CONFIG.sessionStorageKeys.isActive) === 'true';
                // 只有在未聚焦且顺序导航未激活时才更新输入值
                if (document.activeElement !== inputEl && !isNavigating) {
                    inputEl.value = currentPage;
                } else if (isNavigating && document.activeElement !== inputEl) {
                    logDebug("顺序导航激活，输入保留或显示目标。");
                } else if (document.activeElement === inputEl) {
                    logDebug("输入已聚焦，值保留。");
                }
                inputEl.disabled = false;
            } else {
                logError("更新期间未找到输入元素!");
            }

            if (infoEl) {
                infoEl.textContent = `(可见: ${maxVisiblePage || '?'})`;
            } else {
                logError("更新期间未找到信息元素!");
            }

            if (prevBtnEl) {
                prevBtnEl.disabled = (currentPage <= 1);
            } else {
                logError("更新期间未找到上一页按钮元素!");
            }

            if (isNewUI) {
                state.uiInitialized = true;
                logDebug("UI初始化标志设置为true。");
            }
        } catch (e) {
            logError("createOrUpdateUI期间出错:", e);
            state.uiInitialized = false;
        }
    }

    // --- 事件监听器和观察器 ---
    function handleKeyboardShortcut(e) {
        // 检查事件目标是否为输入、文本区域或可编辑元素
        const targetTagName = e.target.tagName.toLowerCase();
        const isTextInput = targetTagName === 'input' || targetTagName === 'textarea' || e.target.isContentEditable;

        // 聚焦快捷键 (Alt + g) - 仍需要Alt，特定目标
        if (e.altKey && !e.ctrlKey && !e.shiftKey && e.key.toLowerCase() === CONFIG.behavior.focusShortcutKey.toLowerCase()) {
            logDebug(`检测到键盘快捷键 (Alt+${CONFIG.behavior.focusShortcutKey})`);
            e.preventDefault();
            try {
                const container = document.getElementById(CONFIG.uiIds.container);
                const input = document.getElementById(CONFIG.uiIds.input);

                if (container && input) {
                    if (container.classList.contains('hidden')) {
                        container.classList.remove('hidden');
                        logDebug("通过快捷键显示UI。");
                    }
                    input.focus();
                    input.select();
                    logDebug("通过快捷键聚焦并选择输入。");

                    // 使用快捷键意味着用户想要控制，取消任何待处理的导航
                    if (sessionStorage.getItem(CONFIG.sessionStorageKeys.isActive) === 'true') {
                        logDebug("使用快捷键，取消待处理的顺序导航。");
                        clearSequentialNavState();
                        flashInputElement(input, 'orange', 500);
                    }

                } else {
                    logError("快捷键的UI元素未找到。尝试重新初始化。");
                    initialize();
                    setTimeout(() => {
                        const inputAfterInit = document.getElementById(CONFIG.uiIds.input);
                        if (inputAfterInit) {
                            inputAfterInit.focus();
                            inputAfterInit.select();
                        } else {
                            logError("即使在重新初始化尝试后也无法聚焦输入。");
                        }
                    }, 150);
                }
            } catch(err) {
                logError("处理聚焦键盘快捷键时出错:", err);
            }
        }
        // 上一页快捷键 (ArrowLeft) - 无Alt，避免干扰文本输入
        else if (!isTextInput && !e.altKey && !e.ctrlKey && !e.metaKey && !e.shiftKey && e.key === CONFIG.behavior.prevPageShortcutKey) {
            logDebug(`检测到上一页快捷键 (${CONFIG.behavior.prevPageShortcutKey})`);
            const prevBtn = document.getElementById(CONFIG.uiIds.prevButton);
            if (prevBtn && !prevBtn.disabled) {
                e.preventDefault();
                logDebug("通过快捷键触发上一页按钮点击。");
                prevBtn.click();
            } else {
                logWarn("快捷键的上一页按钮未找到或已禁用。");
            }
        }
        // 下一页快捷键 (ArrowRight) - 无Alt，避免干扰文本输入
        else if (!isTextInput && !e.altKey && !e.ctrlKey && !e.metaKey && !e.shiftKey && e.key === CONFIG.behavior.nextPageShortcutKey) {
            logDebug(`检测到下一页快捷键 (${CONFIG.behavior.nextPageShortcutKey})`);
            const nextBtn = document.getElementById(CONFIG.uiIds.nextButton);
            if (nextBtn && !nextBtn.disabled) {
                e.preventDefault();
                logDebug("通过快捷键触发下一页按钮点击。");
                nextBtn.click();
            } else {
                logWarn("快捷键的下一页按钮未找到或已禁用。");
            }
        }
    }

    function setupObserver() {
        if (state.observer) {
            return;
        }
        logDebug("设置MutationObserver...");

        const observerCallback = () => {
            if (state.observerTimeout) {
                clearTimeout(state.observerTimeout);
            }

            state.observerTimeout = setTimeout(() => {
                const paginationNow = document.querySelector(CONFIG.selectors.paginationContainer);
                const uiContainer = document.getElementById(CONFIG.uiIds.container);

                // 确保UI反映分页存在/不存在
                if (paginationNow) {
                    if (!uiContainer || !state.uiInitialized) {
                        logDebug("观察器：找到/重新出现分页，确保UI存在。");
                        createOrUpdateUI();
                    } else {
                        createOrUpdateUI();
                    }
                } else {
                    if (uiContainer) {
                        logDebug("观察器：分页消失，移除UI。");
                        uiContainer.remove();
                        state.uiInitialized = false;
                        clearSequentialNavState();
                    }
                }

                // 重要：在潜在的UI更新后检查正在进行的顺序导航
                continueSequentialNavigation();

            }, CONFIG.behavior.observerDebounceMs);
        };

        try {
            state.observer = new MutationObserver(observerCallback);
            state.observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            logDebug("MutationObserver开始观察document body。");
        } catch (e) {
            logError("设置MutationObserver失败:", e);
        }
    }

    // --- 初始化 ---
    function initialize() {
        logDebug("开始初始化...");
        try {
            // 注入样式
            injectStyles();

            // 检查分页是否存在
            const paginationContainer = document.querySelector(CONFIG.selectors.paginationContainer);
            if (paginationContainer) {
                logDebug("找到分页容器，创建UI。");
                createOrUpdateUI();
            } else {
                logDebug("未找到分页容器，跳过UI创建。");
            }

            // 设置观察器
            setupObserver();

            // 添加键盘事件监听器
            document.addEventListener('keydown', handleKeyboardShortcut, true);
            logDebug("键盘事件监听器已添加。");

            // 检查是否有正在进行的顺序导航
            const isNavigating = sessionStorage.getItem(CONFIG.sessionStorageKeys.isActive) === 'true';
            if (isNavigating) {
                logDebug("检测到正在进行的顺序导航，继续...");
                setTimeout(() => {
                    continueSequentialNavigation();
                }, 500); // 给页面一些时间来稳定
            }

            logDebug("初始化完成。");
        } catch (e) {
            logError("初始化期间出错:", e);
        }
    }

    // --- 脚本启动 ---
    function startScript() {
        logDebug("80.lv 导航脚本 v2.9 (JavaScript分页修复版) 启动...");

        if (document.readyState === 'loading') {
            logDebug("文档仍在加载，等待DOMContentLoaded...");
            document.addEventListener('DOMContentLoaded', initialize);
        } else {
            logDebug("文档已准备就绪，立即初始化。");
            initialize();
        }

        // 作为回退，也在window.onload时初始化
        window.addEventListener('load', () => {
            logDebug("Window load事件触发，确保初始化。");
            if (!state.uiInitialized) {
                logDebug("UI未初始化，重新尝试初始化。");
                setTimeout(initialize, 100);
            }
        });
    }

    // 启动脚本
    startScript();

})();