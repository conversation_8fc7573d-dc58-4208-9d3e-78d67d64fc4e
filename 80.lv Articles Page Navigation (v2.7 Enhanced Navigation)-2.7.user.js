// ==UserScript==
// @name         80.lv Articles Page Navigation (v2.8 Enhanced Navigation)
// @namespace    http://tampermonkey.net/
// @version      2.8
// @description  增强 80.lv/articles 页面导航：前进/后退按钮 + 跳转输入框，支持顺序导航到目标页面（优化可见链接检测）
// <AUTHOR> (Revised by AI)
// @match        https://80.lv/articles*
// @exclude      https://80.lv/articles/*/amp* // 排除 AMP 页面
// @icon         https://www.google.com/s2/favicons?sz=64&domain=80.lv
// @grant        none
// @license      MIT
// ==/UserScript==

(function() {
    'use strict';

    // --- 配置 ---
    const CONFIG = {
        // 基于实际测试更新的选择器
        selectors: {
            paginationContainer: 'ul._2pSuN', // 分页容器
            pageLink: 'ul._2pSuN li.E2y4M a._2ZymR', // 页面数字链接
            activePageLink: 'li.E2y4M._3NLRK a._2ZymR', // 当前激活页面链接

            // --- 网站原生前进/后退按钮选择器 ---
            // 更新选择器以更好地兼容按钮和链接元素
            sitePrevButton: 'ul._2pSuN > li:first-child > *._2ZymR', // 前一页按钮
            siteNextButton: 'ul._2pSuN > li:last-child > *._2ZymR', // 下一页按钮

            // 禁用状态检测
            disabledButton: 'button[disabled], .disabled',
            disabledListItem: 'li._25VMy, li.disabled'
        },
        uiIds: {
            container: 'tm-page-nav-container-80lv-v28',
            input: 'tm-page-input-80lv-v28',
            info: 'tm-page-info-80lv-v28',
            prevButton: 'tm-page-prev-button-80lv-v28', // 脚本UI按钮
            nextButton: 'tm-page-next-button-80lv-v28', // 脚本UI按钮
            goButton: 'tm-page-go-button-80lv-v28',
            closeButton: 'tm-page-close-button-80lv-v28',
            styleTag: 'tm-page-nav-styles-80lv-v28'
        },
        behavior: {
            focusShortcutKey: 'g', // Alt + 键
            prevPageShortcutKey: 'ArrowLeft', // 左箭头键
            nextPageShortcutKey: 'ArrowRight', // 右箭头键
            observerDebounceMs: 300, // 减少延迟提高响应性
            pageUrlPattern: '/articles/', // 基于测试结果，所有页面都使用相同URL
            maxRetries: 3, // 最大重试次数
            retryDelay: 1000 // 重试延迟（毫秒）
        },
        sessionStorageKeys: {
            targetPage: 'tm_80lv_targetPage_v28',
            isActive: 'tm_80lv_navigatingActive_v28',
            retryCount: 'tm_80lv_retryCount_v28'
        }
    };

    // --- 状态管理 ---
    let state = {
        uiInitialized: false,
        observer: null,
        observerTimeout: null,
        cachedCurrentPage: 1,
        cachedMaxVisiblePage: 1,
        lastNavigationMethod: null, // 'direct', 'click', 'sequential'
        isNavigating: false, // 防止重复导航
        retryCount: 0 // 重试计数
    };

    // --- 日志记录 ---
    const DEBUG_MODE = true; // 设置为 false 禁用控制台日志
    function logDebug(...args) {
        if (DEBUG_MODE) {
            console.log('[80lv 导航脚本 v2.8]', ...args);
        }
    }
    function logError(...args) {
        console.error('[80lv 导航脚本 v2.8]', ...args);
    }
    function logWarn(...args) {
        console.warn('[80lv 导航脚本 v2.8]', ...args);
    }
    function logInfo(...args) {
        console.info('[80lv 导航脚本 v2.8]', ...args);
    }

    // --- 核心逻辑 ---

    function getCurrentPage() {
        try {
            // 1. 尝试从激活的链接获取当前页面
            const activeLink = document.querySelector(CONFIG.selectors.activePageLink);
            if (activeLink?.textContent) {
                const pageNum = parseInt(activeLink.textContent.trim(), 10);
                if (!isNaN(pageNum) && pageNum > 0) {
                    logDebug('通过激活链接找到当前页面:', pageNum);
                    state.cachedCurrentPage = pageNum;
                    return pageNum;
                }
            }

            // 2. 尝试从URL路径段获取
            const pathSegments = window.location.pathname.split('/');
            const pageIndex = pathSegments.indexOf('page');
            if (pageIndex !== -1 && pageIndex + 1 < pathSegments.length) {
                const potentialPage = parseInt(pathSegments[pageIndex + 1], 10);
                if (!isNaN(potentialPage) && potentialPage > 0) {
                    logDebug('通过URL路径找到当前页面:', potentialPage);
                    state.cachedCurrentPage = potentialPage;
                    return potentialPage;
                }
            }

            // 3. 检查是否为基础 /articles/ 页面（第1页）
            if (window.location.pathname === '/articles' || window.location.pathname === '/articles/') {
                logDebug('当前页面是基础 /articles，设置为第1页');
                state.cachedCurrentPage = 1;
                return 1;
            }

            // 4. 尝试从URL参数获取页面信息
            const urlParams = new URLSearchParams(window.location.search);
            const pageParam = urlParams.get('page');
            if (pageParam) {
                const pageNum = parseInt(pageParam, 10);
                if (!isNaN(pageNum) && pageNum > 0) {
                    logDebug('通过URL参数找到当前页面:', pageNum);
                    state.cachedCurrentPage = pageNum;
                    return pageNum;
                }
            }

            // 5. 回退到缓存或默认值
            logDebug('无法确定当前页面，使用缓存或默认值:', state.cachedCurrentPage);
            return state.cachedCurrentPage || 1;

        } catch (e) {
            logError("获取当前页面时出错:", e);
            return state.cachedCurrentPage || 1;
        }
    }

    function getMaxVisiblePageNumber() {
        let maxPage = 1;
        try {
            const container = document.querySelector(CONFIG.selectors.paginationContainer);
            if (!container) {
                logDebug('未找到分页容器');
                return state.cachedMaxVisiblePage || state.cachedCurrentPage || 1;
            }

            const paginationLinks = container.querySelectorAll(CONFIG.selectors.pageLink);
            if (paginationLinks.length === 0) {
                maxPage = Math.max(1, state.cachedCurrentPage);
                logDebug('未找到页面链接，基于当前页面返回最大页面:', maxPage);
                state.cachedMaxVisiblePage = maxPage;
                return maxPage;
            }

            // 遍历所有分页链接，找到最大页码
            paginationLinks.forEach(link => {
                if (link?.textContent) {
                    const pageNumText = link.textContent.trim();
                    if (/^\d+$/.test(pageNumText)) {
                        const pageNum = parseInt(pageNumText, 10);
                        if (!isNaN(pageNum) && pageNum > maxPage) {
                            maxPage = pageNum;
                        }
                    }
                }
            });

            logDebug('计算得到的最大可见页面:', maxPage);
            state.cachedMaxVisiblePage = maxPage;
            return maxPage;
        } catch (e) {
            logError("获取最大可见页面数时出错:", e);
            maxPage = Math.max(1, state.cachedCurrentPage);
            state.cachedMaxVisiblePage = maxPage;
            return maxPage;
        }
    }

    function buildPageUrl(pageNumber) {
        try {
            // 基于测试结果，80.lv 使用相同的URL结构
            if (pageNumber <= 1) {
                return new URL('/articles/', window.location.origin).href;
            } else {
                // 对于其他页面，可能需要使用查询参数或其他方式
                // 这里保持简单，因为实际测试显示所有页面都使用 /articles/
                return new URL('/articles/', window.location.origin).href;
            }
        } catch (e) {
            logError(`构建页面URL时出错 (页面 ${pageNumber}):`, e);
            return '/articles/';
        }
    }

    function findPageLinkElement(pageNumber) {
        logDebug(`尝试查找页面 ${pageNumber} 的链接元素`);
        try {
            const container = document.querySelector(CONFIG.selectors.paginationContainer);
            if (!container) {
                logDebug("未找到分页容器");
                return null;
            }

            const pageLinks = container.querySelectorAll(CONFIG.selectors.pageLink);
            for (const link of pageLinks) {
                const linkText = link?.textContent?.trim();
                if (linkText && linkText === String(pageNumber)) {
                    logDebug(`找到页面 ${pageNumber} 的匹配链接:`, link);
                    return link;
                }
            }
            logDebug(`未找到页面 ${pageNumber} 的可见链接`);
        } catch (e) {
            logError(`查找页面 ${pageNumber} 链接时出错:`, e);
        }
        return null;
    }

    // --- 改进的按钮状态检测 ---
    function isButtonDisabled(button) {
        if (!button) return true;

        // 检查按钮本身的禁用状态
        if (button.disabled || button.hasAttribute('disabled')) {
            return true;
        }

        // 检查父级 li 元素的禁用类
        const parentLi = button.closest('li');
        if (parentLi) {
            const disabledClasses = ['_25VMy', 'disabled', '_3NLRK'];
            if (disabledClasses.some(cls => parentLi.classList.contains(cls))) {
                return true;
            }
        }

        return false;
    }

    // --- 顺序导航逻辑 ---

    function clearSequentialNavState() {
        logDebug("清除顺序导航状态");
        try {
            sessionStorage.removeItem(CONFIG.sessionStorageKeys.targetPage);
            sessionStorage.removeItem(CONFIG.sessionStorageKeys.isActive);
            sessionStorage.removeItem(CONFIG.sessionStorageKeys.retryCount);
            state.isNavigating = false;
            state.retryCount = 0;
        } catch (e) {
            logError("清除sessionStorage时出错:", e);
        }
    }

    function startSequentialNavigation() {
        const pageInput = document.getElementById(CONFIG.uiIds.input);
        if (!pageInput) {
            logError("顺序导航启动时缺少输入字段");
            return;
        }

        // 防止重复导航
        if (state.isNavigating) {
            logWarn("导航正在进行中，忽略新的导航请求");
            return;
        }

        let targetPage;
        try {
            targetPage = parseInt(pageInput.value, 10);
            logDebug(`请求顺序导航. 输入值: ${pageInput.value}, 解析目标: ${targetPage}`);
        } catch (e) {
            logError("解析页面输入时出错:", e);
            flashInputElement(pageInput, 'red');
            clearSequentialNavState();
            return;
        }

        if (isNaN(targetPage) || targetPage < 1) {
            logError(`无效的目标页面: ${targetPage}`);
            flashInputElement(pageInput, 'red');
            clearSequentialNavState();
            return;
        }

        const currentPage = getCurrentPage();
        if (currentPage === targetPage) {
            logDebug(`页面 ${targetPage} 已经是当前页面，无需导航`);
            flashInputElement(pageInput, 'lightgreen');
            clearSequentialNavState();
            return;
        }

        try {
            logDebug(`存储目标页面=${targetPage} 并激活顺序导航`);
            sessionStorage.setItem(CONFIG.sessionStorageKeys.targetPage, targetPage.toString());
            sessionStorage.setItem(CONFIG.sessionStorageKeys.isActive, 'true');
            sessionStorage.setItem(CONFIG.sessionStorageKeys.retryCount, '0');
            state.lastNavigationMethod = 'sequential';
            state.isNavigating = true;
            flashInputElement(pageInput, 'orange'); // 指示过程开始

            continueSequentialNavigation(); // 立即触发第一步

        } catch (e) {
            logError("设置sessionStorage时出错:", e);
            alert("无法保存导航状态，请查看控制台");
            clearSequentialNavState();
            flashInputElement(pageInput, 'red');
        }
    }

    /**
     * Checks if sequential navigation is active and performs the next step.
     * Optimization: Prioritizes clicking a visible direct page link if available.
     * Falls back to clicking the site's Prev/Next button if the target is not visible.
     */
    function continueSequentialNavigation() {
        let targetPageStr, isActiveStr;
        try {
            targetPageStr = sessionStorage.getItem(CONFIG.sessionStorageKeys.targetPage);
            isActiveStr = sessionStorage.getItem(CONFIG.sessionStorageKeys.isActive);
        } catch (e) { logError("SessionStorage read error:", e); return; } // Don't clear state on read error

        // Exit if not active or state is missing/invalid
        if (isActiveStr !== 'true' || !targetPageStr) {
             // logDebug("Sequential navigation not active or target page missing."); // Can be noisy
             return;
        }

        const targetPage = parseInt(targetPageStr, 10);
        if (isNaN(targetPage)) {
            logError(`Invalid target page in sessionStorage: ${targetPageStr}. Aborting.`);
            clearSequentialNavState();
            return;
        }

        logDebug(`Continuing sequential navigation. Target: ${targetPage}`);
        const pageInput = document.getElementById(CONFIG.uiIds.input); // For feedback
        const currentPage = getCurrentPage();

        // === 1. Completion Check ===
        if (currentPage === targetPage) {
            logDebug(`Sequential navigation complete. Reached target page ${targetPage}.`);
            clearSequentialNavState();
            flashInputElement(pageInput, 'lightgreen', 1500); // Longer success flash
            if (pageInput && document.activeElement !== pageInput) {
                pageInput.value = currentPage; // Update input on arrival
            }
            return;
        }

        // === 2. OPTIMIZATION: Check if Target Page Link is VISIBLE ===
        const directTargetLink = findPageLinkElement(targetPage);
        if (directTargetLink) {
            logDebug(`Target page ${targetPage} link is visible. Clicking directly.`);
            flashInputElement(pageInput, 'cyan', 1000); // Feedback for direct jump
            try {
                // Direct jump successful, clear state BEFORE navigating
                clearSequentialNavState();
                state.lastNavigationMethod = 'click'; // Mark as a click navigation
                directTargetLink.click();
                // Navigation happens, script stops. State is clean for next load.
            } catch (e) {
                logError(`Error clicking direct link for page ${targetPage}:`, e);
                alert(`Error clicking the direct link for page ${targetPage}. Aborting.`);
                clearSequentialNavState(); // Abort on error
                flashInputElement(pageInput, 'red');
            }
            return; // Stop further processing this cycle
        }

        // === 3. Fallback: Target Not Visible - Click Site's Prev/Next ===
        logDebug(`Target page ${targetPage} not visible. Using site Prev/Next button.`);
        let buttonToClick = null;
        let buttonSelector = '';
        let action = '';

        if (targetPage > currentPage) {
            action = 'Next';
            buttonSelector = CONFIG.selectors.siteNextButton;
        } else { // targetPage < currentPage assumed
            action = 'Previous';
            buttonSelector = CONFIG.selectors.sitePrevButton;
        }

        // Verify selectors are not empty (they shouldn't be with the correction, but good practice)
        if (!buttonSelector) {
             logError(`Selector for site's "${action}" button is unexpectedly empty! Aborting.`);
             alert(`Internal script error: Missing selector for site's ${action} button. Aborting navigation.`);
             clearSequentialNavState();
             flashInputElement(pageInput, 'red');
             return;
        }

        try {
            buttonToClick = document.querySelector(buttonSelector);

            // Check if the button/link exists and is not disabled
            // Check for disabled attribute on the element itself
            // Also check if the parent li has a class that might indicate it's disabled
            const parentLi = buttonToClick?.closest('li');
            const hasDisabledClass = parentLi?.classList.contains('disabled') || parentLi?.classList.contains('_3NLRK');
            const isButtonDisabled = buttonToClick?.hasAttribute('disabled') || hasDisabledClass;

             if (buttonToClick && !isButtonDisabled) {
                 logDebug(`Found site's "${action}" button/link using selector "${buttonSelector}". Simulating click...`);
                 flashInputElement(pageInput, 'lightblue'); // Standard step feedback
                 state.lastNavigationMethod = 'sequential'; // Keep marking as sequential step
                 // DO NOT clear state here - we need it for the next page load

                 // Try to use both click() and simulate a mouse click event for better compatibility
                 try {
                     // First try the standard click method
                     buttonToClick.click();

                     // As a fallback, also dispatch a mouse event
                     setTimeout(() => {
                         // If we're still on the same page after a short delay, try a mouse event
                         const mouseEvent = new MouseEvent('click', {
                             bubbles: true,
                             cancelable: true,
                             view: window
                         });
                         buttonToClick.dispatchEvent(mouseEvent);
                         logDebug(`Dispatched additional mouse event on "${action}" button as fallback`);
                     }, 100);
                 } catch (clickErr) {
                     logError(`Error during click operation: ${clickErr}. Trying fallback...`);
                     // Try to navigate directly if clicking fails
                     try {
                         const href = buttonToClick.getAttribute('href');
                         if (href) {
                             window.location.href = href;
                             logDebug(`Used direct href navigation as fallback: ${href}`);
                         }
                     } catch (hrefErr) {
                         logError(`Fallback navigation also failed: ${hrefErr}`);
                         alert(`Could not navigate ${action.toLowerCase()}. Please try manual navigation.`);
                         clearSequentialNavState();
                     }
                 }
                 // Navigation happens, script stops. State remains for next load.
             } else {
                 // Button/link not found, or is disabled
                 if (!buttonToClick) {
                    logWarn(`Site "${action}" button/link not found using selector: "${buttonSelector}". Aborting sequential navigation.`);
                 } else { // isButtonDisabled must be true
                    logWarn(`Site "${action}" button found but is disabled. Aborting sequential navigation.`);
                 }
                 alert(`Could not find or click the site's ${action} button/link (maybe page boundary or selector issue?). Aborting.`);
                 clearSequentialNavState(); // Abort the sequence
                 flashInputElement(pageInput, 'red');
                  if (pageInput && document.activeElement !== pageInput) { pageInput.value = currentPage; } // Reflect current page
             }
        } catch (e) {
            logError(`Error finding/clicking site's "${action}" button/link (Selector: ${buttonSelector}):`, e);
            alert(`Error during ${action} click. Aborting navigation. Check console & selectors.`);
            clearSequentialNavState(); // Abort on error
            flashInputElement(pageInput, 'red');
        }
    }


    // --- Utilities ---

    function flashInputElement(element, color, duration = 750) {
        if (!element) return;
        try {
            const originalOutline = element.style.outline || 'none'; // Store original or default
            const originalTransition = element.style.transition || '';
            element.style.transition = 'outline 0.1s ease-in-out'; // Ensure transition is set
            element.style.outline = `3px solid ${color}`; // Make flash more visible
            setTimeout(() => {
                if (element) {
                    // Restore original style
                    element.style.outline = originalOutline;
                    element.style.transition = originalTransition;
                }
            }, duration);
        } catch (e) {
            logError("Error flashing input element:", e);
        }
    }

    // --- UI Creation and Management ---

    function injectStyles() {
         if (document.getElementById(CONFIG.uiIds.styleTag)) return;
         logDebug("Injecting CSS styles...");
         const css = `
             #${CONFIG.uiIds.container} { position: fixed; bottom: 25px; right: 25px; background-color: rgba(255, 255, 255, 0.97); border: 1px solid #ccc; border-radius: 6px; padding: 10px 12px; box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15); z-index: 10001; display: flex; align-items: center; gap: 8px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; font-size: 14px; line-height: 1.4; color: #333; transition: opacity 0.3s ease, transform 0.3s ease; opacity: 1; transform: translateY(0); }
             #${CONFIG.uiIds.container}.hidden { opacity: 0; transform: translateY(10px); pointer-events: none; }
             #${CONFIG.uiIds.container} label { white-space: nowrap; margin-right: 4px;}
             #${CONFIG.uiIds.input} { width: 55px; padding: 6px 8px; border: 1px solid #ccc; border-radius: 4px; font-size: 14px; text-align: center; }
             #${CONFIG.uiIds.input}:focus { border-color: #555; box-shadow: 0 0 3px rgba(0, 0, 0, 0.1); outline: none; }
             #${CONFIG.uiIds.info} { font-size: 13px; color: #666; white-space: nowrap; user-select: none; margin-left: 4px; margin-right: 8px; }
             #${CONFIG.uiIds.prevButton}, #${CONFIG.uiIds.nextButton}, #${CONFIG.uiIds.goButton} { padding: 6px 10px; background-color: #333; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; transition: background-color 0.2s, opacity 0.2s; white-space: nowrap; }
             #${CONFIG.uiIds.prevButton}:hover, #${CONFIG.uiIds.nextButton}:hover, #${CONFIG.uiIds.goButton}:hover { background-color: #555; }
             #${CONFIG.uiIds.prevButton}:disabled, #${CONFIG.uiIds.nextButton}:disabled { background-color: #999; cursor: not-allowed; opacity: 0.7; }
             #${CONFIG.uiIds.closeButton} { background: none; border: none; color: #777; font-size: 18px; line-height: 1; cursor: pointer; padding: 0 4px; display: flex; align-items: center; justify-content: center; width: 24px; height: 24px; border-radius: 50%; transition: background-color 0.2s, color 0.2s; margin-left: auto; /* Pushes to the right */ }
             #${CONFIG.uiIds.closeButton}:hover { background-color: rgba(0, 0, 0, 0.05); color: #333; }
         `;
         try {
             const styleTag = document.createElement('style');
             styleTag.id = CONFIG.uiIds.styleTag;
             styleTag.textContent = css;
             document.head.appendChild(styleTag);
         } catch (e) {
             logError("Error injecting styles:", e);
         }
    }

    function createOrUpdateUI() {
        logDebug("Attempting to create or update UI...");
        try {
            const currentPage = getCurrentPage();
            const maxVisiblePage = getMaxVisiblePageNumber();

            logDebug(`UI Update State: currentPage=${currentPage}, maxVisiblePage=${maxVisiblePage}`);

            let container = document.getElementById(CONFIG.uiIds.container);
            let isNewUI = false;

            if (!container) {
                logDebug("UI container not found, creating new UI elements.");
                isNewUI = true;
                container = document.createElement('div');
                container.id = CONFIG.uiIds.container;

                const label = document.createElement('label');
                label.htmlFor = CONFIG.uiIds.input;
                label.textContent = 'Page:';
                container.appendChild(label);

                const input = document.createElement('input');
                input.id = CONFIG.uiIds.input;
                input.type = 'number';
                input.min = '1';
                input.placeholder = "Jump";
                input.title = 'Enter page # and press Enter or click Go (uses optimized sequential navigation)';
                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                         clearSequentialNavState(); // Clear any previous state first
                         startSequentialNavigation(); // Start the potentially multi-step process
                         e.preventDefault(); // Prevent form submission if applicable
                    } else {
                         // Any other typing cancels a PENDING navigation from previous page load
                         if (sessionStorage.getItem(CONFIG.sessionStorageKeys.isActive) === 'true') {
                              logDebug("User typed in input, cancelling pending sequential navigation.");
                              clearSequentialNavState();
                         }
                    }
                });
                // Prevent non-numeric characters (optional, but good practice for type=number)
                input.addEventListener('input', function() {
                     this.value = this.value.replace(/[^0-9]/g, '');
                });
                container.appendChild(input);

                const info = document.createElement('span');
                info.id = CONFIG.uiIds.info;
                info.title = 'Highest page number currently visible in site pagination';
                container.appendChild(info);

                // --- SCRIPT's Prev/Next buttons (Now use Site's Buttons) ---
                const prevBtn = document.createElement('button');
                prevBtn.id = CONFIG.uiIds.prevButton;
                prevBtn.textContent = '<';
                prevBtn.title = `Go to previous page (Clicks site button) (← Arrow Key)`; // Updated title
                prevBtn.addEventListener('click', () => {
                    logDebug("Script's Prev button clicked.");
                    clearSequentialNavState(); // Stop sequential if user clicks this
                    const sitePrevButton = document.querySelector(CONFIG.selectors.sitePrevButton);
                    // Check button's disabled property/attribute and parent li's 'disabled' class
                    const parentLiPrev = sitePrevButton?.closest('li');
                    const isPrevDisabled = sitePrevButton?.disabled || sitePrevButton?.hasAttribute('disabled') || parentLiPrev?.classList.contains('disabled');

                    if (sitePrevButton && !isPrevDisabled) {
                         logDebug("Found active site's Prev button. Clicking it.");
                         flashInputElement(prevBtn, 'lightblue');
                         try {
                              sitePrevButton.click();
                         } catch (e) {
                              logError("Error clicking site's Prev button:", e);
                              flashInputElement(prevBtn, 'red');
                         }
                    } else {
                         logWarn("Site's Prev button not found or is disabled.");
                         flashInputElement(prevBtn, 'orange'); // Indicate potential issue
                         prevBtn.disabled = true;
                    }
                });
                container.appendChild(prevBtn);

                const nextBtn = document.createElement('button');
                nextBtn.id = CONFIG.uiIds.nextButton;
                nextBtn.textContent = '>';
                nextBtn.title = `Go to next page (Clicks site button) (→ Arrow Key)`; // Updated title
                nextBtn.addEventListener('click', () => {
                     logDebug("Script's Next button clicked.");
                     clearSequentialNavState(); // Stop sequential if user clicks this
                     const siteNextButton = document.querySelector(CONFIG.selectors.siteNextButton);
                     const parentLiNext = siteNextButton?.closest('li');
                     const isNextDisabled = siteNextButton?.disabled || siteNextButton?.hasAttribute('disabled') || parentLiNext?.classList.contains('disabled');

                     if (siteNextButton && !isNextDisabled) {
                          logDebug("Found active site's Next button. Clicking it.");
                          flashInputElement(nextBtn, 'lightblue');
                         try {
                              siteNextButton.click();
                         } catch (e) {
                              logError("Error clicking site's Next button:", e);
                              flashInputElement(nextBtn, 'red');
                         }
                     } else {
                          logWarn("Site's Next button not found or is disabled.");
                          flashInputElement(nextBtn, 'orange'); // Indicate potential issue
                           nextBtn.disabled = true;
                     }
                });
                container.appendChild(nextBtn);

                // --- Go Button (Starts Sequential Navigation) ---
                const goBtn = document.createElement('button');
                goBtn.id = CONFIG.uiIds.goButton;
                goBtn.textContent = 'Go';
                goBtn.title = 'Navigate sequentially (optimized) to the specified page';
                goBtn.addEventListener('click', () => {
                     clearSequentialNavState(); // Clear any previous state first
                     startSequentialNavigation(); // Start the potentially multi-step process
                });
                container.appendChild(goBtn);

                const closeBtn = document.createElement('button');
                closeBtn.id = CONFIG.uiIds.closeButton;
                closeBtn.innerHTML = '×'; // Multiplication sign for 'close'
                closeBtn.title = `Hide navigation control (Alt+${CONFIG.behavior.focusShortcutKey} to focus/show)`;
                closeBtn.addEventListener('click', () => {
                    if (container) container.classList.add('hidden');
                     clearSequentialNavState(); // Also clear state if user hides UI
                });
                container.appendChild(closeBtn);

                container.title = "Use < / > for direct page change, or enter page # + Go/Enter for optimized sequential navigation.";
                document.body.appendChild(container);
                logDebug("New UI elements added to body.");

            } else {
                logDebug("UI container found, updating existing elements.");
            }

            // --- Update UI State (runs for both new and existing UI) ---
            const inputEl = document.getElementById(CONFIG.uiIds.input);
            const infoEl = document.getElementById(CONFIG.uiIds.info);
            const prevBtnEl = document.getElementById(CONFIG.uiIds.prevButton);

            if (inputEl) {
                const isNavigating = sessionStorage.getItem(CONFIG.sessionStorageKeys.isActive) === 'true';
                // Only update input value if NOT focused AND sequential nav is NOT active
                if (document.activeElement !== inputEl && !isNavigating) {
                    inputEl.value = currentPage;
                } else if (isNavigating && document.activeElement !== inputEl) {
                     // Optionally keep the TARGET page in the input during navigation if not focused
                     // const targetPage = sessionStorage.getItem(CONFIG.sessionStorageKeys.targetPage);
                     // inputEl.value = targetPage || currentPage; // Display target if available
                     logDebug("Sequential navigation active, input preserved or shows target.");
                } else if (document.activeElement === inputEl) {
                     logDebug("Input is focused, value preserved.");
                }
                inputEl.disabled = false; // Ensure not disabled
            } else { logError("Input element not found during update!"); }

            if (infoEl) {
                infoEl.textContent = `(vis: ${maxVisiblePage || '?'})`; // Show max visible or ?
            } else { logError("Info element not found during update!"); }

            if (prevBtnEl) {
                prevBtnEl.disabled = (currentPage <= 1);
            } else { logError("Prev button element not found during update!"); }

            // Next button disabling is tricky without knowing total pages, so keep enabled
            // const nextBtnEl = document.getElementById(CONFIG.uiIds.nextButton);
            // if (nextBtnEl) { nextBtnEl.disabled = ???; }

            if (isNewUI) {
                state.uiInitialized = true;
                logDebug("UI Initialized flag set to true.");
            }
        } catch (e) {
            logError("Error during createOrUpdateUI:", e);
            state.uiInitialized = false; // Mark as not initialized on error
        }
    }

    // --- Event Listeners and Observer ---

    function handleKeyboardShortcut(e) {
        // Check if the event target is an input, textarea, or contenteditable element
        const targetTagName = e.target.tagName.toLowerCase();
        const isTextInput = targetTagName === 'input' || targetTagName === 'textarea' || e.target.isContentEditable;

        // Focus shortcut (Alt + g) - Still requires Alt, specific target
        if (e.altKey && !e.ctrlKey && !e.shiftKey && e.key.toLowerCase() === CONFIG.behavior.focusShortcutKey.toLowerCase()) {
            logDebug(`Keyboard shortcut detected (Alt+${CONFIG.behavior.focusShortcutKey})`);
            e.preventDefault(); // Prevent default browser action (e.g., opening menu)
            try {
                const container = document.getElementById(CONFIG.uiIds.container);
                const input = document.getElementById(CONFIG.uiIds.input);

                if (container && input) {
                     if (container.classList.contains('hidden')) {
                          container.classList.remove('hidden'); // Show if hidden
                           logDebug("UI shown via shortcut.");
                     }
                     // Always focus and select on shortcut
                    input.focus();
                    input.select();
                    logDebug("Input focused and selected via shortcut.");

                    // Using shortcut implies user wants control, cancel any pending navigation
                    if (sessionStorage.getItem(CONFIG.sessionStorageKeys.isActive) === 'true') {
                         logDebug("Shortcut used, cancelling pending sequential navigation.");
                         clearSequentialNavState();
                         flashInputElement(input, 'orange', 500); // Brief flash to indicate cancellation
                     }

                } else {
                    logError("UI elements not found for shortcut. Attempting re-initialization.");
                    initialize(); // Try to rebuild UI if missing
                    setTimeout(() => { // Try focus again after potential rebuild
                        const inputAfterInit = document.getElementById(CONFIG.uiIds.input);
                        if (inputAfterInit) {
                             inputAfterInit.focus();
                             inputAfterInit.select();
                        } else {
                             logError("Could not focus input even after re-initialization attempt.");
                        }
                    }, 150);
                }
            } catch(err) {
                 logError("Error handling focus keyboard shortcut:", err);
            }
        }
        // Previous Page Shortcut (ArrowLeft) - No Alt, Avoid interfering with text input
        else if (!isTextInput && !e.altKey && !e.ctrlKey && !e.metaKey && !e.shiftKey && e.key === CONFIG.behavior.prevPageShortcutKey) {
            logDebug(`Previous Page Shortcut detected (${CONFIG.behavior.prevPageShortcutKey})`);
            const prevBtn = document.getElementById(CONFIG.uiIds.prevButton);
            if (prevBtn && !prevBtn.disabled) {
                 e.preventDefault(); // Prevent default browser actions like scrolling
                logDebug("Triggering Previous Page button click via shortcut.");
                prevBtn.click(); // Simulate click on the script's Prev button
            } else {
                logWarn("Previous Page button not found or disabled for shortcut.");
            }
        }
        // Next Page Shortcut (ArrowRight) - No Alt, Avoid interfering with text input
        else if (!isTextInput && !e.altKey && !e.ctrlKey && !e.metaKey && !e.shiftKey && e.key === CONFIG.behavior.nextPageShortcutKey) {
            logDebug(`Next Page Shortcut detected (${CONFIG.behavior.nextPageShortcutKey})`);
            const nextBtn = document.getElementById(CONFIG.uiIds.nextButton);
            if (nextBtn && !nextBtn.disabled) {
                 e.preventDefault(); // Prevent default browser actions like scrolling
                logDebug("Triggering Next Page button click via shortcut.");
                nextBtn.click(); // Simulate click on the script's Next button
            } else {
                logWarn("Next Page button not found or disabled for shortcut.");
            }
        }
    }

    function setupObserver() {
        if (state.observer) {
             // logDebug("MutationObserver already exists."); // Noisy
             return;
        }
        logDebug("Setting up MutationObserver...");

        const observerCallback = (/* mutationsList, observer */) => {
            // Basic check: Simple debounce on any body mutation. Could be refined if needed.
            if (state.observerTimeout) {
                clearTimeout(state.observerTimeout);
            }

            // logDebug("MutationObserver detected changes. Debouncing UI update/check..."); // Noisy
            state.observerTimeout = setTimeout(() => {
                // logDebug("Debounced observer action starting..."); // Noisy
                const paginationNow = document.querySelector(CONFIG.selectors.paginationContainer);
                const uiContainer = document.getElementById(CONFIG.uiIds.container);

                // Ensure UI reflects pagination presence/absence
                if (paginationNow) {
                    if (!uiContainer || !state.uiInitialized) {
                         logDebug("Observer: Pagination found/reappeared, ensuring UI exists.");
                         createOrUpdateUI(); // Create or update if pagination is present
                    } else {
                         // Both exist, update UI state (e.g., current page number)
                         // logDebug("Observer: Pagination and UI exist, updating UI state."); // Noisy
                         createOrUpdateUI();
                    }
                } else {
                     // logDebug("Observer: Pagination container not found after debounce."); // Noisy
                     if (uiContainer) {
                         logDebug("Observer: Pagination gone, removing UI.");
                         uiContainer.remove();
                         state.uiInitialized = false;
                         clearSequentialNavState(); // Clean state if UI/pagination disappears
                     }
                }

                // IMPORTANT: Check for ongoing sequential navigation AFTER potential UI updates
                // This handles dynamic content loading that might reveal the target page link
                // or require the next Prev/Next click.
                // logDebug("Observer: Checking for sequential navigation continuation."); // Noisy
                continueSequentialNavigation();

            }, CONFIG.behavior.observerDebounceMs);
        };

        try {
            state.observer = new MutationObserver(observerCallback);
            // Observe the body for added/removed nodes in the whole subtree
            state.observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            logDebug("MutationObserver started observing document body.");
        } catch (e) {
             logError("Failed to setup MutationObserver:", e);
        }
    }

    // --- Initialization ---

    function initialize() {
        logDebug("Initializing script...");

        // Inject styles once if needed
        injectStyles();

        // Check current state of pagination
        const paginationContainer = document.querySelector(CONFIG.selectors.paginationContainer);

        if (paginationContainer) {
            logDebug("Pagination container found during initialization.");
            // Create or update UI based on current DOM state
            createOrUpdateUI();

            // Check if we need to continue navigating from a previous step
            // Call this *after* UI is potentially created so feedback elements exist
            logDebug("Initialization: Checking for sequential navigation continuation.");
            continueSequentialNavigation();

        } else {
            logDebug("Pagination container NOT found during initial check. Observer will handle appearance.");
            // Ensure any stale UI from previous state is removed
            const existingContainer = document.getElementById(CONFIG.uiIds.container);
             if (existingContainer) {
                 logDebug("Removing stale UI because pagination is missing on init.");
                 existingContainer.remove();
                 state.uiInitialized = false;
                 clearSequentialNavState(); // Clean state if UI is removed
             }
        }

         // Setup keyboard listener safely (remove first to avoid duplicates, then add)
         document.removeEventListener('keydown', handleKeyboardShortcut);
         document.addEventListener('keydown', handleKeyboardShortcut);
         logDebug("Keyboard shortcut listener attached.");

         // Ensure observer is running to catch dynamic changes (like pagination appearing later)
         setupObserver();
         logDebug("Initialization complete.");
    }

    // --- Startup ---
    logDebug(`Script execution started (v${GM_info.script.version}). Waiting for DOM ready state...`);

    function runInitialization() {
        logDebug(`Document readyState: ${document.readyState}`);
        // Use 'interactive' or 'complete' as reliable signals that the main DOM is ready
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            logDebug("Initializing on complete/interactive state.");
            initialize();
        } else {
            logDebug("Adding DOMContentLoaded listener for initialization.");
            // DOMContentLoaded is generally preferred over 'load' as it fires earlier
            window.addEventListener('DOMContentLoaded', () => {
                logDebug('DOMContentLoaded fired. Initializing...');
                initialize();
            }, { once: true });
            // Add 'load' as a fallback in case DOMContentLoaded fails or script injected very late
             window.addEventListener('load', () => {
                 if (!state.uiInitialized && !document.getElementById(CONFIG.uiIds.container)) {
                      logWarn('Load event fired - Initializing as fallback.');
                      initialize();
                 }
             }, { once: true });
        }
        // Failsafe timeout: Initialize after a delay if events don't fire or are missed
        setTimeout(() => {
            if (!state.uiInitialized && !document.getElementById(CONFIG.uiIds.container)) {
                logWarn("Initializing via timeout fallback (1.5s). DOM events might have been missed.");
                initialize();
            } else {
                 // logDebug("Initialization fallback timeout: Already initialized or UI container exists."); // Noisy
            }
        }, 1500); // 1.5 seconds timeout
    }

    // Add a small delay (~100ms) before the very first check/run.
    // This can sometimes help avoid race conditions with the page's own scripts.
    setTimeout(runInitialization, 100);

})();