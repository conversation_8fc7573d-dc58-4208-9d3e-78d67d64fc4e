{"config": {"configFile": "C:\\Users\\<USER>\\Desktop\\80.lv_articles\\playwright.config.js", "rootDir": "C:/Users/<USER>/Desktop/80.lv_articles/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", null], ["list", null], ["json", {"outputFile": "test-results.json"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Desktop/80.lv_articles/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Desktop/80.lv_articles/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/80.lv_articles/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Desktop/80.lv_articles/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/80.lv_articles/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Desktop/80.lv_articles/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 8, "webServer": null}, "suites": [], "errors": [{"message": "Error: No tests found", "stack": "Error: No tests found"}], "stats": {"startTime": "2025-05-25T08:15:10.954Z", "duration": 11.91599999999994, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}