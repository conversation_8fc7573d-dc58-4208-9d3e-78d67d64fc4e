const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function testScript() {
    console.log('🚀 开始测试 80.lv 导航脚本...');
    
    // 读取用户脚本
    const userScriptPath = path.join(__dirname, '80.lv Articles Page Navigation (v2.8 Enhanced Navigation)-2.8.user.js');
    const userScriptContent = fs.readFileSync(userScriptPath, 'utf8');
    
    // 启动浏览器
    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 1000 // 减慢操作以便观察
    });
    
    const page = await browser.newPage();
    
    try {
        // 注入用户脚本
        await page.addInitScript(userScriptContent);
        console.log('✅ 用户脚本已注入');
        
        // 导航到80.lv articles页面
        console.log('🌐 导航到 https://80.lv/articles...');
        await page.goto('https://80.lv/articles');
        
        // 等待页面加载
        await page.waitForLoadState('networkidle');
        console.log('✅ 页面加载完成');
        
        // 等待脚本初始化
        await page.waitForTimeout(3000);
        
        // 检查UI是否出现
        try {
            await page.waitForSelector('#tm-page-nav-container-80lv-v28', { timeout: 10000 });
            console.log('✅ 导航UI已显示');
            
            // 检查各个UI元素
            const elements = [
                '#tm-page-input-80lv-v28',
                '#tm-page-prev-button-80lv-v28',
                '#tm-page-next-button-80lv-v28',
                '#tm-page-go-button-80lv-v28',
                '#tm-page-info-80lv-v28'
            ];
            
            for (const selector of elements) {
                const element = await page.locator(selector);
                if (await element.isVisible()) {
                    console.log(`✅ ${selector} 可见`);
                } else {
                    console.log(`❌ ${selector} 不可见`);
                }
            }
            
            // 测试键盘快捷键
            console.log('🔧 测试键盘快捷键 Alt+G...');
            await page.keyboard.press('Alt+g');
            await page.waitForTimeout(1000);
            
            const input = page.locator('#tm-page-input-80lv-v28');
            if (await input.isFocused()) {
                console.log('✅ Alt+G 快捷键工作正常');
            } else {
                console.log('❌ Alt+G 快捷键未工作');
            }
            
            // 测试分页信息
            const info = page.locator('#tm-page-info-80lv-v28');
            const infoText = await info.textContent();
            console.log(`📊 分页信息: ${infoText}`);
            
            // 测试前进/后退按钮状态
            const prevButton = page.locator('#tm-page-prev-button-80lv-v28');
            const nextButton = page.locator('#tm-page-next-button-80lv-v28');
            
            const prevDisabled = await prevButton.isDisabled();
            const nextDisabled = await nextButton.isDisabled();
            
            console.log(`🔄 上一页按钮禁用: ${prevDisabled}`);
            console.log(`🔄 下一页按钮禁用: ${nextDisabled}`);
            
            // 如果下一页按钮可用，测试点击
            if (!nextDisabled) {
                console.log('🔧 测试下一页按钮...');
                await nextButton.click();
                await page.waitForTimeout(3000);
                console.log('✅ 下一页按钮点击完成');
            }
            
            console.log('🎉 所有测试完成！');
            
        } catch (error) {
            console.log('❌ 导航UI未出现:', error.message);
            
            // 检查是否有分页容器
            const paginationExists = await page.locator('ul._2pSuN').count() > 0;
            console.log(`📄 分页容器存在: ${paginationExists}`);
            
            // 检查控制台错误
            const logs = await page.evaluate(() => {
                return window.console.logs || [];
            });
            console.log('📝 控制台日志:', logs);
        }
        
        // 保持浏览器打开一段时间以便观察
        console.log('⏳ 保持浏览器打开10秒以便观察...');
        await page.waitForTimeout(10000);
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await browser.close();
        console.log('🔚 测试完成，浏览器已关闭');
    }
}

// 运行测试
testScript().catch(console.error);
